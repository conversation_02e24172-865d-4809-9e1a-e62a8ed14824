.checkout-accessories-page {
  display: flex;
  flex-direction: column;
  height: auto;
  width: auto;
  padding: 100px 38px 38px 38px;
  background-color: var(--bg-color);
}

.checkout-accessories-page section {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 20px;
}

.checkout-accessories-page .checkout-form {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 60vw;
  align-self: center;
  padding: 24px;
  margin-top: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.3);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.checkout-accessories-page form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
}

.checkout-accessories-page form fieldset {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.checkout-accessories-page fieldset label:not(.upload-image-btn) {
  color: var(--secondary-text-color);
  font-weight: 600;
}

.checkout-accessories-page fieldset .location {
  border-radius: 10px;
  background-color: rgba(211, 211, 211, 0.5);
  cursor: not-allowed;
}

.checkout-accessories-page input:not(input[type="radio"]),
.checkout-accessories-page select,
.checkout-accessories-page textarea {
  width: 100%;
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
}

.checkout-accessories-page textarea {
  height: 13vh;
}

.checkout-accessories-page .checkout-form fieldset p {
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  font-size: 0.875rem;
  color: var(--secondary-text-color);
}

.checkout-accessories-page .employee-radio-container,
.checkout-accessories-page .location-radio-container {
  display: flex;
  justify-content: start;
  align-items: center;
  width: 100%;
  padding: 10px 20px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  gap: 10px;
}

.checkout-accessories-page input[type="radio"] {
  accent-color: var(--primary-color);
}

.checkout-accessories-page .employee-radio-container img,
.checkout-accessories-page .location-radio-container img {
  height: 20px;
  left: 20px;
}

.checkout-accessories-page .save-btn {
  justify-content: center;
  align-items: center;
  height: 48px;
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  transition: 0.5s ease;
  cursor: pointer;
}

.checkout-accessories-page .save-btn:hover {
  background-color: var(--primary-color-hover);
}

.checkout-accessories-page .images-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}

.checkout-accessories-page .image-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100px;
  border-radius: 10px;
  position: relative;
}

.checkout-accessories-page .image-selected img:not(.image-selected button img) {
  display: flex;
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 15px;
  padding: 2px;
}

.checkout-accessories-page .image-selected button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 20px;
  padding: 10px;
  border-radius: 50px;
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  border: 2px solid #ffffff;
  cursor: pointer;
}

.checkout-accessories-page .image-selected button:hover {
  background-color: darkred;
}

.checkout-accessories-page .image-selected button img {
  height: 12px;
  width: 12px;
}

.checkout-accessories-page .upload-image-btn {
  display: flex;
  width: fit-content;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  border-radius: 40px;
  padding: 12px;
  font-size: 0.83333rem;
  color: var(--bg-color);
  cursor: pointer;
  transition: 0.5s ease;
}

.checkout-accessories-page .upload-image-btn:hover {
  background-color: var(--primary-color-hover);
}
