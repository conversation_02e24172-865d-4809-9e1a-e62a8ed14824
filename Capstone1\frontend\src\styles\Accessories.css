/* Fix for all tables */
.page table,
.accessories-page table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Fix for specific table classes */
.page table.products-table,
.page table.assets-table,
.accessories-page table.accessories-table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Fix for all table cells */
.page table th,
.page table td,
.accessories-page table th,
.accessories-page table td {
  border-radius: 0 !important;
}

/* Fix for specific table cells */
.page table.products-table th,
.page table.products-table td,
.page table.assets-table th,
.page table.assets-table td,
.accessories-page table.accessories-table th,
.accessories-page table.accessories-table td {
  border-radius: 0 !important;
}

/* Fix for first and last rows */
.page table tr:first-child th,
.page table tr:first-child td,
.accessories-page table tr:first-child th,
.accessories-page table tr:first-child td {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.page table tr:last-child th,
.page table tr:last-child td,
.accessories-page table tr:last-child th,
.accessories-page table tr:last-child td {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

/* Fix for first and last cells in each row */
.page table tr th:first-child,
.page table tr td:first-child,
.accessories-page table tr th:first-child,
.accessories-page table tr td:first-child {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.page table tr th:last-child,
.page table tr td:last-child,
.accessories-page table tr th:last-child,
.accessories-page table tr td:last-child {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

<<<<<<< HEAD
.accessories-page .accessories-image {
  height: 5vh;
  width: 5vw;
  object-fit: cover;
=======
/* Fix for corner cells */
.page table tr:first-child th:first-child,
.page table tr:first-child td:first-child,
.accessories-page table tr:first-child th:first-child,
.accessories-page table tr:first-child td:first-child {
  border-top-left-radius: 0 !important;
>>>>>>> origin/UI-Capstone
}

.page table tr:first-child th:last-child,
.page table tr:first-child td:last-child,
.accessories-page table tr:first-child th:last-child,
.accessories-page table tr:first-child td:last-child {
  border-top-right-radius: 0 !important;
}

.page table tr:last-child th:first-child,
.page table tr:last-child td:first-child,
.accessories-page table tr:last-child th:first-child,
.accessories-page table tr:last-child td:first-child {
  border-bottom-left-radius: 0 !important;
}

.page table tr:last-child th:last-child,
.page table tr:last-child td:last-child,
.accessories-page table tr:last-child th:last-child,
.accessories-page table tr:last-child td:last-child {
  border-bottom-right-radius: 0 !important;
}

/* Fix for the container to not affect the table corners */
.accessories-page .middle {
  border-radius: 0 !important;
  overflow: visible !important;
}