.accessories-registration {
  display: flex;
  flex-direction: column;
  height: auto;
  width: auto;
  padding: 100px 38px 38px 38px;
  background-color: var(--bg-color);
  overflow: auto;
}

.hide-scroll {
  height: 100vh;
  overflow-y: hidden;
}

.accessories-registration section {
  display: flex;
}

.accessories-registration .registration-form {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 60vw;
  align-self: center;
  padding: 24px;
  margin-top: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.3);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  gap: 20px;
}

.accessories-registration form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
}

.accessories-registration form fieldset {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.accessories-registration fieldset .dropdown-container,
.accessories-registration fieldset .purchase-cost-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.accessories-registration fieldset label:not(.upload-image-btn) {
  color: var(--secondary-text-color);
  font-weight: 600;
}

.accessories-registration input,
.accessories-registration select,
.accessories-registration textarea {
  width: 100%;
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
}

.accessories-registration fieldset p {
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  font-size: 0.875rem;
  color: var(--secondary-text-color);
}

.accessories-registration .save-btn {
  height: 48px;
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  transition: 0.5s ease;
  cursor: pointer;
}

.accessories-registration .save-btn:hover {
  background-color: var(--primary-color-hover);
}

.accessories-registration .save-btn:disabled {
  cursor: no-drop;
  background-color: #d3d3d3;
}

.accessories-registration .image-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100px;
  border-radius: 10px;
  position: relative;
}

.accessories-registration .image-selected img:not(.image-selected button img) {
  display: flex;
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 15px;
  padding: 2px;
}

.accessories-registration .image-selected button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 20px;
  padding: 10px;
  border-radius: 50px;
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  border: 2px solid #ffffff;
  cursor: pointer;
}

.accessories-registration .image-selected button:hover {
  background-color: darkred;
}

.accessories-registration .image-selected button img {
  height: 12px;
  width: 12px;
}

.accessories-registration .upload-image-btn {
  display: flex;
  width: fit-content;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  border-radius: 40px;
  padding: 12px;
  font-size: 0.83333rem;
  color: var(--bg-color);
  cursor: pointer;
  transition: 0.5s ease;
}

.accessories-registration .upload-image-btn:hover {
  background-color: var(--primary-color-hover);
}

.accessories-registration span {
  color: red;
  font-size: 0.875rem;
}

.custom-dropdown {
  width: 100%;
  border-radius: 10px;
  font-size: 0.875rem;
  padding: 3px 8px;
  border: 1px solid #ccc;
  background-color: white;
  color: grey;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg fill='gray' height='12' viewBox='0 0 24 24' width='12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
}

.custom-dropdown option {
  font-size: 0.875rem;
  color: grey;
}

.custom-dropdown option:checked {
  color: white;
  background-color: #007bff; /* mimic selection highlight */
}

.input-error {
  border-color: red;
}
