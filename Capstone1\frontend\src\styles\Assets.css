/* Ensure the table has proper spacing */
.page table.assets-table {
  border-collapse: collapse;
  border-spacing: 0;
  border-radius: 0;
}

/* Remove any potential border-radius from table cells */
.page table.assets-table th,
.page table.assets-table td {
  border-radius: 0;
}

/* Specific styles for Assets table action columns */
.page table.assets-table td:nth-child(8),
.page table.assets-table td:nth-child(9),
.page table.assets-table td:nth-child(10) {
  padding: 4px;
  width: 40px !important;
  text-align: center;
}

.page table.assets-table th:nth-child(8),
.page table.assets-table th:nth-child(9),
.page table.assets-table th:nth-child(10) {
  width: 40px !important;
  padding: 4px;
  text-align: center;
}

/* Ensure buttons are properly centered in their cells */
.page table.assets-table td:nth-child(8) button,
.page table.assets-table td:nth-child(9) button,
.page table.assets-table td:nth-child(10) button {
  display: block;
  margin: 0 auto;
  padding: 4px;
  width: 26px;
  height: 26px;
}

/* Add specific spacing for the action columns */
.page table.assets-table th:nth-child(8),
.page table.assets-table th:nth-child(9),
.page table.assets-table th:nth-child(10) {
  padding-left: 8px;
  padding-right: 8px;
  min-width: 40px;
}

.page table.assets-table td:nth-child(8),
.page table.assets-table td:nth-child(9),
.page table.assets-table td:nth-child(10) {
  padding-left: 8px;
  padding-right: 8px;
}

/* Ensure the check-in/check-out button has proper styling */
.check-in-btn,
.check-out-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  color: white;
  font-weight: 500;
  cursor: pointer;
}

.check-in-btn {
  background-color: #007bff;
}

.check-out-btn {
  background-color: #28a745;
}