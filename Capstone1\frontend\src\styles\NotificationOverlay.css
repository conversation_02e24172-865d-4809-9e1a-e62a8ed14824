.notification-overlay {
  position: absolute;
  top: 45px;
  right: 0;
  z-index: 1000;
  width: 400px;
  max-width: 90vw;
}

.notification-container {
  background-color: white;
  border-radius: 40px;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  border: 1px solid #d3d3d3;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #E5E7EB;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.notification-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--secondary-text-color);
  margin: 0;
}

.clear-all-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-all-btn:hover {
  background-color: var(--primary-color-hover);
}

.clear-all-btn:disabled {
  background-color: #E5E7EB;
  color: #9CA3AF;
  cursor: not-allowed;
}

.notification-list {
  overflow-y: auto;
  max-height: calc(80vh - 60px);
  padding: 0;
}

.notification-item {
  display: flex;
  padding: 16px 24px;
  border-bottom: 1px solid #E5E7EB;
  position: relative;
  align-items: flex-start;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: rgba(211, 211, 211, 0.1);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  margin-right: 16px;
  flex-shrink: 0;
  padding: 0;
}

.notification-content {
  flex: 1;
}

.notification-content h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--secondary-text-color);
}

.notification-content p {
  font-size: 14px;
  margin: 0 0 8px 0;
  color: var(--secondary-text-color);
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #6B7280;
}

.delete-notification-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.delete-notification-btn:hover svg {
  fill: #EF4444;
}

.no-notifications {
  padding: 24px;
  text-align: center;
  color: #6B7280;
}

/* Customize icons for different notification types */
.notification-icon svg {
  width: 20px;
  height: 20px;
}

/* Type-specific colors */
.notification-icon {
  background-color: var(--primary-color);
}
