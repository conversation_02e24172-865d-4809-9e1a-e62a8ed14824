.perform-audit-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: auto;
  padding: 100px 38px 38px 38px;
  background-color: var(--bg-color);
  overflow-y: auto;
}

.fix-scrollbar {
  height: 100vh;
}

.perform-audit-page section {
  display: flex;
  flex-direction: row;
  height: auto;
  width: 100%;
  gap: 20px;
}

.perform-audit-page .perform-audit-form {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 60vw;
  align-self: center;
  padding: 24px;
  margin-top: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.3);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.perform-audit-page form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
}

.perform-audit-page form fieldset {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.perform-audit-page fieldset label:not(.upload-image-btn) {
  color: var(--secondary-text-color);
  font-weight: 600;
}

.perform-audit-form fieldset span {
  color: red;
  font-size: 0.875rem;
}

.perform-audit-page input,
.perform-audit-page select,
.perform-audit-page textarea {
  width: 100%;
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
}

.perform-audit-page textarea {
  height: 13vh;
}

.perform-audit-page .perform-audit-form fieldset p {
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  font-size: 0.875rem;
  color: var(--secondary-text-color);
}

.perform-audit-page .save-btn {
  justify-content: center;
  align-items: center;
  height: 48px;
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  transition: 0.5s ease;
  cursor: pointer;
}

.perform-audit-page button:disabled {
  cursor: not-allowed;
  background-color: #d3d3d3;
}

.perform-audit-page .save-btn:hover:not(button:disabled) {
  background-color: var(--primary-color-hover);
}

.perform-audit-page .images-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}

.perform-audit-page .image-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100px;
  border-radius: 10px;
  position: relative;
}

.perform-audit-page .image-selected img:not(.image-selected button img) {
  display: flex;
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 15px;
  padding: 2px;
}

.perform-audit-page .image-selected button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 20px;
  padding: 10px;
  border-radius: 50px;
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  border: 2px solid #ffffff;
  cursor: pointer;
}

.perform-audit-page .image-selected button:hover {
  background-color: darkred;
}

.perform-audit-page .image-selected button img {
  height: 12px;
  width: 12px;
}

.perform-audit-page .upload-image-btn {
  display: flex;
  width: fit-content;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  border-radius: 40px;
  padding: 12px;
  font-size: 0.83333rem;
  color: var(--bg-color);
  cursor: pointer;
  transition: 0.5s ease;
}

.perform-audit-page .upload-image-btn:hover {
  background-color: var(--primary-color-hover);
}
