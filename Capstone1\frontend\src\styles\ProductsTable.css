/* Products table specific styles for action buttons */

/* Action columns width and alignment */
.products-table th:nth-child(8),
.products-table th:nth-child(9),
.products-table th:nth-child(10),
.products-table td:nth-child(8),
.products-table td:nth-child(9),
.products-table td:nth-child(10) {
  width: 40px !important;
  text-align: center;
  padding: 0 !important;
}

/* Create a container for action buttons */
.products-action-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0; /* Remove gap between buttons */
}

/* Action buttons styling */
.products-edit-btn,
.products-delete-btn,
.products-view-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px; /* Reduce padding */
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 -2px; /* Negative margin to bring buttons closer */
}

/* SVG icons styling */
.products-edit-btn svg,
.products-delete-btn svg,
.products-view-btn svg {
  width: 16px;
  height: 16px;
}

/* Hover effects */
.products-edit-btn:hover,
.products-view-btn:hover {
  color: var(--primary-color);
}

.products-delete-btn:hover {
  color: #ff0000;
}

/* Group the action buttons together */
.products-action-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0;
  margin: 0;
  padding: 0;
}
