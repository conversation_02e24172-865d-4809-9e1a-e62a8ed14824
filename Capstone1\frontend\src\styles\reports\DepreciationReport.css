.depreciation-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.depreciation-content {
  flex-grow: 1;
  padding: 2rem 4rem;
  margin-top: 60px;
}

.depreciation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.depreciation-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.period-selector span {
  color: #6B7280;
  font-size: 14px;
}

.period-selector select {
  padding: 0.5rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background-color: white;
  cursor: pointer;
}

.depreciation-table-container {
  background: white;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  overflow: hidden;
}

.depreciation-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.depreciation-table th {
  background-color: rgba(211, 211, 211, 0.2);
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--secondary-text-color);
  border-bottom: 1px solid #d3d3d3;
  text-transform: uppercase;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.depreciation-table td {
  padding: 0.75rem 1rem;
  font-size: 0.88rem;
  color: var(--secondary-text-color);
  border-bottom: 1px solid #d3d3d3;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.depreciation-table tbody tr:hover {
  background-color: rgba(211, 211, 211, 0.1);
}

.action-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #6B7280;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.action-button:hover {
  background-color: #F3F4F6;
}

/* Asset link styling */
.asset-cell {
  font-weight: 500;
}

.asset-link {
  color: var(--primary-color);
  text-decoration: none;
}

.asset-link:hover {
  text-decoration: underline;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination button:hover:not(:disabled) {
  background-color: #F3F4F6;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  font-size: 14px;
  color: #6B7280;
}

/* Column width adjustments for the last three columns */
.depreciation-table th:nth-last-child(1),
.depreciation-table td:nth-last-child(1) {
  width: 8%;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
}

.depreciation-table th:nth-last-child(2),
.depreciation-table td:nth-last-child(2) {
  width: 12%;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
}

.depreciation-table th:nth-last-child(3),
.depreciation-table td:nth-last-child(3) {
  width: 10%;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
}