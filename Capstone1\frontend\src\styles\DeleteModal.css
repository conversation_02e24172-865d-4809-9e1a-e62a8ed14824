.delete-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 101;
}

.delete-modal .overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.15); /* Reduced opacity from 0.3 to 0.15 */
  z-index: 999; /* Just below the modal content */
  backdrop-filter: blur(1px); /* Slight blur effect */
}

.delete-modal .content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: fit-content;
  width: 400px;
  max-width: 90%;
  background-color: white;
  padding: 40px 20px;
  border-radius: 20px;
  border: 1px solid #d3d3d3;
  position: fixed; /* Changed from absolute to fixed */
  gap: 15px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
  z-index: 1001; /* Ensure it's above the overlay */
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0; /* Ensure no margin affects positioning */
}

.delete-modal .close-button {
  height: 28px;
  width: 28px;
  padding: 5px;
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  position: absolute;
  right: 25px;
  top: 15px;
  transition: 0.3s ease;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.delete-modal .close-button img {
  width: 18px;
  height: 18px;
  filter: invert(1); /* This will make the SVG icon black */
}

.delete-modal .close-button:hover {
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.delete-modal img:not(.close-button img) {
  height: 50px;
  width: 50px;
  background-color: rgba(255, 59, 48, 0.08);
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 10px;
}

.delete-modal h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
}

.delete-modal p {
  font-size: 0.95rem;
  color: #666;
  text-align: center;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.delete-modal button {
  padding: 12px 16px;
  border-radius: 40px;
  cursor: pointer;
  width: 50%;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.delete-modal .content div {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
  gap: 20px;
  margin-top: 10px;
  padding-top: 5px;
}

.delete-modal .confirm-button {
  background-color: #ff3b30;
  color: white;
  border: none;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(255, 59, 48, 0.2);
  transition: all 0.2s ease;
}

.delete-modal .confirm-button:hover {
  background-color: #e0352b;
  box-shadow: 0 4px 8px rgba(255, 59, 48, 0.3);
  transform: translateY(-1px);
}

.delete-modal .confirm-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(255, 59, 48, 0.2);
}

.delete-modal .cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.delete-modal .cancel-button:hover {
  background-color: #e0e0e0;
  color: #333;
  border-color: #d3d3d3;
  transform: translateY(-1px);
}

.delete-modal .cancel-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
