/* AccessoriesAvailability.css - Specific styles for the availability display in Accessories table */

.accessories-availability {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.accessories-availability span {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 5px;
  color: #34c759;
  font-weight: 500;
  white-space: nowrap;
  overflow: visible;
}

.accessories-progress {
  display: flex;
  height: 8px;
  width: 60px;
  border-radius: 8px;
  margin-left: 5px;
  border: none;
  overflow: hidden;
}

/* Background of the progress bar */
.accessories-progress::-webkit-progress-bar {
  background-color: rgba(0, 123, 255, 0.2);
  border-radius: 10px;
}

/* Color of the progress fill */
.accessories-progress::-webkit-progress-value {
  background-color: #34c759;
  border-radius: 10px;
}

/* For Firefox */
.accessories-progress::-moz-progress-bar {
  background-color: #34c759;
  border-radius: 10px;
}

/* For IE */
.accessories-progress {
  color: #34c759;
}
